{% extends "base.html" %}

{% block title %}控制台 - 故事讲述器{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-speedometer2"></i> 故事优化工作台</h2>
    <div class="btn-group" role="group">
        <a href="{{ url_for('main.quick_adapt') }}" class="btn btn-primary">
            <i class="bi bi-lightning"></i> 快速优化
        </a>
        <a href="{{ url_for('main.upload') }}" class="btn btn-outline-primary">
            <i class="bi bi-cloud-upload"></i> 上传文件
        </a>
    </div>
</div>

{% if tasks %}
    <div class="row">
        {% for task in tasks %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card task-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h5 class="card-title mb-0">{{ task.task_name }}</h5>
                        {% if task.status == 'completed' %}
                            <span class="badge bg-success status-badge">已完成</span>
                        {% elif task.status == 'processing' %}
                            <span class="badge bg-warning status-badge">处理中</span>
                        {% elif task.status == 'failed' %}
                            <span class="badge bg-danger status-badge">失败</span>
                        {% else %}
                            <span class="badge bg-secondary status-badge">等待中</span>
                        {% endif %}
                    </div>

                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="bi bi-file-text"></i> {{ task.original_filename }}
                        </small>
                    </div>

                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="bi bi-person"></i> 主角：{{ task.character }}
                        </small>
                    </div>

                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="bi bi-book"></i> 书名：{{ task.book_name }}
                        </small>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="bi bi-tag"></i> {{ task.channel }} / {{ task.person }}人称
                        </small>
                    </div>

                    {% if task.status == 'processing' %}
                    <div class="progress-container mb-3">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" 
                                 style="width: {{ task.progress }}%"
                                 id="progress-{{ task.id }}">
                            </div>
                        </div>
                        <div class="progress-text" id="progress-text-{{ task.id }}">
                            {{ task.progress }}%
                        </div>
                    </div>
                    {% endif %}

                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="bi bi-clock"></i> 
                            创建时间：{{ task.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </small>
                    </div>

                    {% if task.total_chapters > 0 %}
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="bi bi-list-ol"></i> 
                            章节进度：{{ task.processed_chapters }}/{{ task.total_chapters }}
                        </small>
                    </div>
                    {% endif %}
                </div>

                <div class="card-footer bg-transparent">
                    <div class="d-flex gap-2">
                        <a href="{{ url_for('main.task_detail', task_id=task.id) }}" 
                           class="btn btn-outline-primary btn-sm flex-fill">
                            <i class="bi bi-eye"></i> 查看详情
                        </a>
                        
                        {% if task.is_completed and task.output_path %}
                        <a href="{{ url_for('main.download_result', task_id=task.id) }}" 
                           class="btn btn-success btn-sm">
                            <i class="bi bi-download"></i> 下载
                        </a>
                        {% endif %}
                        
                        <form method="POST" action="{{ url_for('main.delete_task', task_id=task.id) }}"
                              class="d-inline" onsubmit="return confirmDelete({{ task.id }})">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="bi bi-trash"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="bi bi-book display-1 text-muted"></i>
        <h4 class="mt-3 text-muted">还没有故事优化任务</h4>
        <p class="text-muted">选择一种方式开始您的故事优化之旅</p>
        <div class="d-flex gap-3 justify-content-center mt-4">
            <a href="{{ url_for('main.quick_adapt') }}" class="btn btn-primary">
                <i class="bi bi-lightning"></i> 快速优化
            </a>
            <a href="{{ url_for('main.upload') }}" class="btn btn-outline-primary">
                <i class="bi bi-cloud-upload"></i> 上传文件
            </a>
        </div>
        <div class="mt-4">
            <small class="text-muted">
                <strong>快速优化</strong>：直接粘贴文本内容，立即开始优化<br>
                <strong>上传文件</strong>：上传TXT文件，适合较长的内容
            </small>
        </div>
    </div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
// 删除确认函数
function confirmDelete(taskId) {
    console.log('删除任务请求:', taskId);
    const confirmed = confirm('确定要删除这个任务吗？这将删除所有相关文件。');
    if (confirmed) {
        console.log('用户确认删除任务:', taskId);
    } else {
        console.log('用户取消删除任务:', taskId);
    }
    return confirmed;
}

// 自动刷新处理中任务的进度
function updateTaskProgress() {
    const processingTasks = document.querySelectorAll('[id^="progress-"]');

    // 如果没有处理中的任务，停止轮询
    if (processingTasks.length === 0) {
        console.log('没有处理中的任务，停止轮询');
        if (window.progressInterval) {
            clearInterval(window.progressInterval);
            window.progressInterval = null;
        }
        return;
    }

    processingTasks.forEach(element => {
        const taskId = element.id.split('-')[1];

        fetch(`/api/task/${taskId}/status`)
            .then(response => {
                if (!response.ok) {
                    // 如果是404错误，说明任务已被删除
                    if (response.status === 404) {
                        console.log(`任务 ${taskId} 已被删除，刷新页面`);
                        location.reload();
                        return;
                    }
                    throw new Error(`HTTP ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // 检查API错误
                if (data.error) {
                    console.error(`Task ${taskId} status error:`, data.error);
                    // 如果是任务不存在的错误，刷新页面
                    if (data.error.includes('not found') || data.error.includes('不存在')) {
                        console.log(`任务 ${taskId} 不存在，刷新页面`);
                        location.reload();
                    }
                    return;
                }

                if (data.status === 'processing') {
                    const progressBar = document.getElementById(`progress-${taskId}`);
                    const progressText = document.getElementById(`progress-text-${taskId}`);

                    if (progressBar && progressText && data.progress !== undefined) {
                        progressBar.style.width = `${data.progress}%`;
                        progressText.textContent = `${data.progress}%`;

                        if (data.chapters_current !== undefined && data.chapters_total) {
                            progressText.textContent = `${data.progress}% (${data.chapters_current}/${data.chapters_total})`;
                        }
                    }
                } else if (data.status === 'completed' || data.status === 'failed') {
                    // 任务完成或失败，刷新页面
                    location.reload();
                }
            })
            .catch(error => {
                console.error(`Error updating progress for task ${taskId}:`, error);
                // 网络错误可能是因为任务被删除，尝试刷新页面
                if (error.message.includes('404')) {
                    console.log(`任务 ${taskId} 可能已被删除，刷新页面`);
                    location.reload();
                }
            });
    });
}

// 启动轮询
function startProgressPolling() {
    // 检查是否有处理中的任务
    const processingTasks = document.querySelectorAll('[id^="progress-"]');
    if (processingTasks.length > 0) {
        console.log(`发现 ${processingTasks.length} 个处理中的任务，启动轮询`);
        // 每5秒更新一次进度
        window.progressInterval = setInterval(updateTaskProgress, 5000);
        // 立即更新一次
        updateTaskProgress();
    } else {
        console.log('没有处理中的任务，不启动轮询');
    }
}

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // 页面隐藏时暂停轮询
        if (window.progressInterval) {
            console.log('页面隐藏，暂停轮询');
            clearInterval(window.progressInterval);
            window.progressInterval = null;
        }
    } else {
        // 页面显示时恢复轮询
        if (!window.progressInterval) {
            console.log('页面显示，恢复轮询');
            startProgressPolling();
        }
    }
});

// 页面加载时启动轮询
document.addEventListener('DOMContentLoaded', startProgressPolling);
</script>
{% endblock %}
